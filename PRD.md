# Product Requirements Document (PRD)
## Nur Contractor - Airbnb-like Booking Platform Backend API

### 1. Project Overview

**Project Name:** Nur Contractor Backend API  
**Platform Type:** Airbnb-like booking platform for properties and cars  
**Technology Stack:** Rust (Backend API)  
**Data Storage:** JSON files / Hardcoded data (Phase 1)  
**Target Users:** Guests (booking properties/cars), Hosts (listing properties/cars)

### 2. Core Features & Functionality

#### 2.1 User Management
- **User Registration & Authentication**
  - Guest registration/login
  - Host registration/login
  - JWT-based authentication
  - User profile management
  - Password reset functionality

#### 2.2 Property Management
- **Property Listings**
  - Create, read, update, delete properties
  - Property details (title, description, location, amenities)
  - Property images (URLs)
  - Pricing per night
  - Availability calendar
  - Property categories (apartment, house, villa, etc.)
  - Maximum guests capacity

- **Property Search & Filtering**
  - Search by location
  - Filter by price range
  - Filter by property type
  - Filter by amenities
  - Filter by guest capacity
  - Date availability check

#### 2.3 Car Rental Management
- **Car Listings**
  - Create, read, update, delete cars
  - Car details (make, model, year, description)
  - Car images (URLs)
  - Pricing per day
  - Availability calendar
  - Car categories (economy, luxury, SUV, etc.)
  - Features (GPS, AC, automatic/manual)

- **Car Search & Filtering**
  - Search by location
  - Filter by price range
  - Filter by car type
  - Filter by features
  - Date availability check

#### 2.4 Booking System
- **Property Bookings**
  - Create booking requests
  - Check availability
  - Calculate total cost
  - Booking confirmation
  - Booking cancellation
  - Booking history

- **Car Bookings**
  - Create rental requests
  - Check availability
  - Calculate total cost
  - Booking confirmation
  - Booking cancellation
  - Rental history

#### 2.5 Review & Rating System
- **Property Reviews**
  - Submit reviews for properties
  - Rate properties (1-5 stars)
  - View property reviews
  - Average rating calculation

- **Car Reviews**
  - Submit reviews for cars
  - Rate cars (1-5 stars)
  - View car reviews
  - Average rating calculation

### 3. API Endpoints Structure

#### 3.1 Authentication Endpoints
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
POST /api/auth/forgot-password
POST /api/auth/reset-password
```

#### 3.2 User Management Endpoints
```
GET /api/users/profile
PUT /api/users/profile
GET /api/users/{user_id}
```

#### 3.3 Property Endpoints
```
GET /api/properties
GET /api/properties/{property_id}
POST /api/properties
PUT /api/properties/{property_id}
DELETE /api/properties/{property_id}
GET /api/properties/search
GET /api/properties/{property_id}/availability
```

#### 3.4 Car Endpoints
```
GET /api/cars
GET /api/cars/{car_id}
POST /api/cars
PUT /api/cars/{car_id}
DELETE /api/cars/{car_id}
GET /api/cars/search
GET /api/cars/{car_id}/availability
```

#### 3.5 Booking Endpoints
```
POST /api/bookings/properties
POST /api/bookings/cars
GET /api/bookings
GET /api/bookings/{booking_id}
PUT /api/bookings/{booking_id}/cancel
GET /api/bookings/history
```

#### 3.6 Review Endpoints
```
POST /api/reviews/properties/{property_id}
POST /api/reviews/cars/{car_id}
GET /api/reviews/properties/{property_id}
GET /api/reviews/cars/{car_id}
```

### 4. Data Models

#### 4.1 User Model
```json
{
  "id": "uuid",
  "email": "string",
  "password_hash": "string",
  "first_name": "string",
  "last_name": "string",
  "phone": "string",
  "role": "guest|host|admin",
  "profile_image": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

#### 4.2 Property Model
```json
{
  "id": "uuid",
  "host_id": "uuid",
  "title": "string",
  "description": "string",
  "property_type": "apartment|house|villa|condo",
  "address": {
    "street": "string",
    "city": "string",
    "state": "string",
    "country": "string",
    "postal_code": "string",
    "latitude": "float",
    "longitude": "float"
  },
  "price_per_night": "decimal",
  "max_guests": "integer",
  "bedrooms": "integer",
  "bathrooms": "integer",
  "amenities": ["wifi", "parking", "pool", "gym"],
  "images": ["string"],
  "availability": [
    {
      "date": "date",
      "available": "boolean"
    }
  ],
  "average_rating": "float",
  "total_reviews": "integer",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

#### 4.3 Car Model
```json
{
  "id": "uuid",
  "owner_id": "uuid",
  "make": "string",
  "model": "string",
  "year": "integer",
  "category": "economy|luxury|suv|sports",
  "transmission": "automatic|manual",
  "fuel_type": "petrol|diesel|electric|hybrid",
  "seats": "integer",
  "price_per_day": "decimal",
  "features": ["gps", "ac", "bluetooth", "backup_camera"],
  "images": ["string"],
  "location": {
    "city": "string",
    "state": "string",
    "country": "string",
    "latitude": "float",
    "longitude": "float"
  },
  "availability": [
    {
      "date": "date",
      "available": "boolean"
    }
  ],
  "average_rating": "float",
  "total_reviews": "integer",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### 5. Technical Requirements

#### 5.1 Framework & Libraries
- **Web Framework:** Axum or Actix-web
- **Serialization:** Serde
- **Authentication:** JWT tokens
- **Validation:** Validator crate
- **UUID Generation:** uuid crate
- **Date/Time:** chrono crate
- **HTTP Client:** reqwest (if needed)

#### 5.2 Data Storage (Phase 1)
- JSON files for persistent storage
- In-memory data structures for runtime
- File-based session management

#### 5.3 Security Requirements
- JWT token authentication
- Password hashing (bcrypt)
- Input validation and sanitization
- CORS configuration
- Rate limiting

#### 5.4 Error Handling
- Standardized error responses
- Proper HTTP status codes
- Detailed error messages for development
- Generic error messages for production

### 6. Development Phases

#### Phase 1: Core API Development
- Set up Rust project structure
- Implement basic CRUD operations
- User authentication system
- Property and car management
- Basic booking functionality

#### Phase 2: Advanced Features
- Search and filtering
- Review and rating system
- Advanced booking features
- File upload handling

#### Phase 3: Optimization & Testing
- Performance optimization
- Comprehensive testing
- API documentation
- Error handling improvements

### 7. Success Criteria
- All API endpoints functional
- Proper authentication and authorization
- Data validation and error handling
- Clean, maintainable code structure
- Comprehensive API documentation
- Ready for database integration in future phases
