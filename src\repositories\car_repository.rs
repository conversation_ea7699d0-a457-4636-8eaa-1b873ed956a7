use async_trait::async_trait;
use chrono::NaiveDate;
use uuid::Uuid;

use crate::errors::Result;
use crate::models::{Car, CarSearchParams, CarCategory, Transmission, FuelType};
use super::json_storage::FileJsonStorage;

#[async_trait]
pub trait CarRepositoryTrait: Send + Sync {
    async fn create(&self, car: Car) -> Result<Car>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Car>>;
    async fn find_by_owner_id(&self, owner_id: Uuid) -> Result<Vec<Car>>;
    async fn update(&self, car: Car) -> Result<Option<Car>>;
    async fn delete(&self, id: Uuid) -> Result<bool>;
    async fn list(&self) -> Result<Vec<Car>>;
    async fn search(&self, params: CarSearchParams) -> Result<Vec<Car>>;
    async fn find_available(&self, start_date: NaiveDate, end_date: NaiveDate) -> Result<Vec<Car>>;
}

#[derive(Clone)]
pub struct CarRepository {
    storage: FileJsonStorage<Car>,
}

impl CarRepository {
    pub async fn new(file_path: &str) -> Result<Self> {
        let storage = FileJsonStorage::new(file_path).await?;
        Ok(Self { storage })
    }
}

#[async_trait]
impl CarRepositoryTrait for CarRepository {
    async fn create(&self, car: Car) -> Result<Car> {
        self.storage.create_with_id(car.id, car).await
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<Car>> {
        self.storage.find_by_id(id).await
    }

    async fn find_by_owner_id(&self, owner_id: Uuid) -> Result<Vec<Car>> {
        self.storage
            .find_by(|car| car.owner_id == owner_id)
            .await
    }

    async fn update(&self, car: Car) -> Result<Option<Car>> {
        self.storage.update(car.id, car).await
    }

    async fn delete(&self, id: Uuid) -> Result<bool> {
        self.storage.delete(id).await
    }

    async fn list(&self) -> Result<Vec<Car>> {
        self.storage.list().await
    }

    async fn search(&self, params: CarSearchParams) -> Result<Vec<Car>> {
        let cars = self.storage.list().await?;
        
        let filtered: Vec<Car> = cars
            .into_iter()
            .filter(|car| {
                // Filter by city
                if let Some(ref city) = params.city {
                    if !car.location.city.to_lowercase().contains(&city.to_lowercase()) {
                        return false;
                    }
                }

                // Filter by country
                if let Some(ref country) = params.country {
                    if !car.location.country.to_lowercase().contains(&country.to_lowercase()) {
                        return false;
                    }
                }

                // Filter by category
                if let Some(ref category) = params.category {
                    if car.category != *category {
                        return false;
                    }
                }

                // Filter by transmission
                if let Some(ref transmission) = params.transmission {
                    if car.transmission != *transmission {
                        return false;
                    }
                }

                // Filter by fuel type
                if let Some(ref fuel_type) = params.fuel_type {
                    if car.fuel_type != *fuel_type {
                        return false;
                    }
                }

                // Filter by price range
                if let Some(min_price) = params.min_price {
                    if car.price_per_day < min_price {
                        return false;
                    }
                }

                if let Some(max_price) = params.max_price {
                    if car.price_per_day > max_price {
                        return false;
                    }
                }

                // Filter by minimum seats
                if let Some(min_seats) = params.min_seats {
                    if car.seats < min_seats {
                        return false;
                    }
                }

                // Filter by features
                if let Some(ref features) = params.features {
                    for feature in features {
                        if !car.features.contains(feature) {
                            return false;
                        }
                    }
                }

                // Filter by availability
                if let (Some(pickup_date), Some(return_date)) = (params.pickup_date, params.return_date) {
                    if !car.is_available(pickup_date, return_date) {
                        return false;
                    }
                }

                true
            })
            .collect();

        Ok(filtered)
    }

    async fn find_available(&self, start_date: NaiveDate, end_date: NaiveDate) -> Result<Vec<Car>> {
        self.storage
            .find_by(|car| car.is_available(start_date, end_date))
            .await
    }
}
