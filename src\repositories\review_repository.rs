use async_trait::async_trait;
use uuid::Uuid;

use crate::errors::Result;
use crate::models::{Review, ReviewSearchParams, BookingType};
use super::json_storage::FileJsonStorage;

#[async_trait]
pub trait ReviewRepositoryTrait: Send + Sync {
    async fn create(&self, review: Review) -> Result<Review>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Review>>;
    async fn find_by_resource_id(&self, resource_id: Uuid) -> Result<Vec<Review>>;
    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Review>>;
    async fn update(&self, review: Review) -> Result<Option<Review>>;
    async fn delete(&self, id: Uuid) -> Result<bool>;
    async fn list(&self) -> Result<Vec<Review>>;
    async fn search(&self, resource_id: Uuid, params: ReviewSearchParams) -> Result<Vec<Review>>;
    async fn find_by_resource_and_user(&self, resource_id: Uuid, user_id: Uuid) -> Result<Option<Review>>;
}

#[derive(Clone)]
pub struct ReviewRepository {
    storage: FileJsonStorage<Review>,
}

impl ReviewRepository {
    pub async fn new(file_path: &str) -> Result<Self> {
        let storage = FileJsonStorage::new(file_path).await?;
        Ok(Self { storage })
    }
}

#[async_trait]
impl ReviewRepositoryTrait for ReviewRepository {
    async fn create(&self, review: Review) -> Result<Review> {
        self.storage.create_with_id(review.id, review).await
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<Review>> {
        self.storage.find_by_id(id).await
    }

    async fn find_by_resource_id(&self, resource_id: Uuid) -> Result<Vec<Review>> {
        self.storage
            .find_by(|review| review.resource_id == resource_id)
            .await
    }

    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Review>> {
        self.storage
            .find_by(|review| review.user_id == user_id)
            .await
    }

    async fn update(&self, review: Review) -> Result<Option<Review>> {
        self.storage.update(review.id, review).await
    }

    async fn delete(&self, id: Uuid) -> Result<bool> {
        self.storage.delete(id).await
    }

    async fn list(&self) -> Result<Vec<Review>> {
        self.storage.list().await
    }

    async fn search(&self, resource_id: Uuid, params: ReviewSearchParams) -> Result<Vec<Review>> {
        let reviews = self.find_by_resource_id(resource_id).await?;
        
        let filtered: Vec<Review> = reviews
            .into_iter()
            .filter(|review| {
                // Filter by rating
                if let Some(rating) = params.rating {
                    if review.rating != rating {
                        return false;
                    }
                }

                // Filter by user
                if let Some(user_id) = params.user_id {
                    if review.user_id != user_id {
                        return false;
                    }
                }

                true
            })
            .collect();

        Ok(filtered)
    }

    async fn find_by_resource_and_user(&self, resource_id: Uuid, user_id: Uuid) -> Result<Option<Review>> {
        self.storage
            .find_one_by(|review| review.resource_id == resource_id && review.user_id == user_id)
            .await
    }
}
