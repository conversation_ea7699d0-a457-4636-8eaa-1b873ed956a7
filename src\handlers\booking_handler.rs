use axum::{
    extract::{Path, Query, State, Extension},
    response::<PERSON><PERSON>,
};
use uuid::Uuid;

use crate::{
    errors::Result,
    models::{
        CreatePropertyBookingRequest, CreateCarBookingRequest, BookingSearchParams,
        ApiResponse, BookingResponse, PaginatedResponse
    },
    middleware::auth::AuthUser,
    AppState,
};

pub async fn create_property_booking(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreatePropertyBookingRequest>,
) -> Result<Json<ApiResponse<BookingResponse>>> {
    let booking = state
        .booking_service
        .create_property_booking(auth_user.id, request, &state.property_service.property_repository)
        .await?;
    Ok(Json(ApiResponse::success(booking)))
}

pub async fn create_car_booking(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    <PERSON><PERSON>(request): <PERSON><PERSON><CreateCarBookingRequest>,
) -> Result<Json<ApiResponse<BookingResponse>>> {
    let booking = state
        .booking_service
        .create_car_booking(auth_user.id, request, &state.car_service.car_repository)
        .await?;
    Ok(Json(ApiResponse::success(booking)))
}

pub async fn get_booking(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(booking_id): Path<Uuid>,
) -> Result<Json<ApiResponse<BookingResponse>>> {
    let booking = state
        .booking_service
        .get_booking(booking_id, auth_user.id)
        .await?;
    Ok(Json(ApiResponse::success(booking)))
}

pub async fn cancel_booking(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(booking_id): Path<Uuid>,
) -> Result<Json<ApiResponse<BookingResponse>>> {
    let booking = state
        .booking_service
        .cancel_booking(booking_id, auth_user.id)
        .await?;
    Ok(Json(ApiResponse::success(booking)))
}

pub async fn list_bookings(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<BookingSearchParams>,
) -> Result<Json<ApiResponse<PaginatedResponse<BookingResponse>>>> {
    let bookings = state
        .booking_service
        .list_user_bookings(auth_user.id, params)
        .await?;
    Ok(Json(ApiResponse::success(bookings)))
}

pub async fn get_booking_history(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<ApiResponse<Vec<BookingResponse>>>> {
    let bookings = state
        .booking_service
        .get_booking_history(auth_user.id)
        .await?;
    Ok(Json(ApiResponse::success(bookings)))
}
