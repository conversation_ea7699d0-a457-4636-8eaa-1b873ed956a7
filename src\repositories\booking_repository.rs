use async_trait::async_trait;
use uuid::Uuid;

use crate::errors::Result;
use crate::models::{Booking, BookingSearchParams, BookingStatus, BookingType};
use super::json_storage::FileJsonStorage;

#[async_trait]
pub trait BookingRepositoryTrait: Send + Sync {
    async fn create(&self, booking: Booking) -> Result<Booking>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Booking>>;
    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Booking>>;
    async fn find_by_resource_id(&self, resource_id: Uuid) -> Result<Vec<Booking>>;
    async fn update(&self, booking: Booking) -> Result<Option<Booking>>;
    async fn delete(&self, id: Uuid) -> Result<bool>;
    async fn list(&self) -> Result<Vec<Booking>>;
    async fn search(&self, user_id: Uuid, params: BookingSearchParams) -> Result<Vec<Booking>>;
}

#[derive(Clone)]
pub struct BookingRepository {
    storage: FileJsonStorage<Booking>,
}

impl BookingRepository {
    pub async fn new(file_path: &str) -> Result<Self> {
        let storage = FileJsonStorage::new(file_path).await?;
        Ok(Self { storage })
    }
}

#[async_trait]
impl BookingRepositoryTrait for BookingRepository {
    async fn create(&self, booking: Booking) -> Result<Booking> {
        self.storage.create_with_id(booking.id, booking).await
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<Booking>> {
        self.storage.find_by_id(id).await
    }

    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Booking>> {
        self.storage
            .find_by(|booking| booking.user_id == user_id)
            .await
    }

    async fn find_by_resource_id(&self, resource_id: Uuid) -> Result<Vec<Booking>> {
        self.storage
            .find_by(|booking| booking.resource_id == resource_id)
            .await
    }

    async fn update(&self, booking: Booking) -> Result<Option<Booking>> {
        self.storage.update(booking.id, booking).await
    }

    async fn delete(&self, id: Uuid) -> Result<bool> {
        self.storage.delete(id).await
    }

    async fn list(&self) -> Result<Vec<Booking>> {
        self.storage.list().await
    }

    async fn search(&self, user_id: Uuid, params: BookingSearchParams) -> Result<Vec<Booking>> {
        let bookings = self.find_by_user_id(user_id).await?;
        
        let filtered: Vec<Booking> = bookings
            .into_iter()
            .filter(|booking| {
                // Filter by status
                if let Some(ref status) = params.status {
                    if booking.status != *status {
                        return false;
                    }
                }

                // Filter by booking type
                if let Some(ref booking_type) = params.booking_type {
                    if booking.booking_type != *booking_type {
                        return false;
                    }
                }

                // Filter by date range
                if let Some(start_date) = params.start_date {
                    if booking.start_date < start_date {
                        return false;
                    }
                }

                if let Some(end_date) = params.end_date {
                    if booking.end_date > end_date {
                        return false;
                    }
                }

                true
            })
            .collect();

        Ok(filtered)
    }
}
