mod config;
mod errors;
mod models;
mod repositories;
mod services;
mod handlers;
mod middleware;
mod utils;

use axum::{
    routing::{get, post, put, delete},
    Router,
    http::Method,
};
use std::net::SocketAddr;
use tower::ServiceBuilder;
use tower_http::{
    cors::{Any, CorsLayer},
    trace::TraceLayer,
};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::{
    config::Config,
    repositories::{
        user_repository::UserRepository,
        property_repository::PropertyRepository,
        car_repository::CarRepository,
        booking_repository::BookingRepository,
        review_repository::ReviewRepository,
    },
    services::{
        auth_service::AuthService,
        user_service::UserService,
        property_service::PropertyService,
        car_service::CarService,
        booking_service::BookingService,
        review_service::ReviewService,
    },
    handlers::{
        auth_handler,
        user_handler,
        property_handler,
        car_handler,
        booking_handler,
        review_handler,
    },
    middleware::auth::auth_middleware,
};

#[derive(Clone)]
pub struct AppState {
    pub config: Config,
    pub auth_service: AuthService,
    pub user_service: UserService,
    pub property_service: PropertyService,
    pub car_service: CarService,
    pub booking_service: BookingService,
    pub review_service: ReviewService,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "nur_contractor_api=debug,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Load configuration
    let config = Config::new()?;
    
    // Initialize repositories
    let user_repo = UserRepository::new("data/users.json").await?;
    let property_repo = PropertyRepository::new("data/properties.json").await?;
    let car_repo = CarRepository::new("data/cars.json").await?;
    let booking_repo = BookingRepository::new("data/bookings.json").await?;
    let review_repo = ReviewRepository::new("data/reviews.json").await?;

    // Initialize services
    let auth_service = AuthService::new(user_repo.clone(), config.clone());
    let user_service = UserService::new(user_repo);
    let property_service = PropertyService::new(property_repo);
    let car_service = CarService::new(car_repo);
    let booking_service = BookingService::new(booking_repo);
    let review_service = ReviewService::new(review_repo);

    // Create application state
    let app_state = AppState {
        config: config.clone(),
        auth_service,
        user_service,
        property_service,
        car_service,
        booking_service,
        review_service,
    };

    // Build our application with routes
    let app = create_router(app_state);

    // Run the server
    let addr = SocketAddr::from(([0, 0, 0, 0], config.port));
    tracing::info!("Server starting on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

fn create_router(state: AppState) -> Router {
    Router::new()
        // Health check
        .route("/health", get(|| async { "OK" }))
        
        // Auth routes (public)
        .route("/api/auth/register", post(auth_handler::register))
        .route("/api/auth/login", post(auth_handler::login))
        .route("/api/auth/refresh", post(auth_handler::refresh_token))
        
        // Protected routes
        .route("/api/auth/logout", post(auth_handler::logout))
        .route("/api/users/profile", get(user_handler::get_profile))
        .route("/api/users/profile", put(user_handler::update_profile))
        .route("/api/users/:user_id", get(user_handler::get_user))
        
        // Property routes
        .route("/api/properties", get(property_handler::list_properties))
        .route("/api/properties", post(property_handler::create_property))
        .route("/api/properties/search", get(property_handler::search_properties))
        .route("/api/properties/:property_id", get(property_handler::get_property))
        .route("/api/properties/:property_id", put(property_handler::update_property))
        .route("/api/properties/:property_id", delete(property_handler::delete_property))
        .route("/api/properties/:property_id/availability", get(property_handler::get_availability))
        
        // Car routes
        .route("/api/cars", get(car_handler::list_cars))
        .route("/api/cars", post(car_handler::create_car))
        .route("/api/cars/search", get(car_handler::search_cars))
        .route("/api/cars/:car_id", get(car_handler::get_car))
        .route("/api/cars/:car_id", put(car_handler::update_car))
        .route("/api/cars/:car_id", delete(car_handler::delete_car))
        .route("/api/cars/:car_id/availability", get(car_handler::get_availability))
        
        // Booking routes
        .route("/api/bookings/properties", post(booking_handler::create_property_booking))
        .route("/api/bookings/cars", post(booking_handler::create_car_booking))
        .route("/api/bookings", get(booking_handler::list_bookings))
        .route("/api/bookings/:booking_id", get(booking_handler::get_booking))
        .route("/api/bookings/:booking_id/cancel", put(booking_handler::cancel_booking))
        .route("/api/bookings/history", get(booking_handler::get_booking_history))
        
        // Review routes
        .route("/api/reviews/properties/:property_id", post(review_handler::create_property_review))
        .route("/api/reviews/cars/:car_id", post(review_handler::create_car_review))
        .route("/api/reviews/properties/:property_id", get(review_handler::get_property_reviews))
        .route("/api/reviews/cars/:car_id", get(review_handler::get_car_reviews))
        
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(
                    CorsLayer::new()
                        .allow_origin(Any)
                        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
                        .allow_headers(Any),
                )
                .layer(axum::middleware::from_fn_with_state(
                    state.clone(),
                    auth_middleware,
                )),
        )
        .with_state(state)
}
