use chrono::{DateTime, Utc, NaiveDate};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;

use super::common::{Address, Availability, PropertyType, PaginationParams};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Property {
    pub id: Uuid,
    pub host_id: Uuid,
    pub title: String,
    pub description: String,
    pub property_type: PropertyType,
    pub address: Address,
    pub price_per_night: Decimal,
    pub max_guests: u32,
    pub bedrooms: u32,
    pub bathrooms: u32,
    pub amenities: Vec<String>,
    pub images: Vec<String>,
    pub availability: Vec<Availability>,
    pub average_rating: Option<f64>,
    pub total_reviews: u32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreatePropertyRequest {
    #[validate(length(min = 1, max = 200))]
    pub title: String,
    #[validate(length(min = 10, max = 2000))]
    pub description: String,
    pub property_type: PropertyType,
    pub address: Address,
    #[validate(range(min = 0.01))]
    pub price_per_night: Decimal,
    #[validate(range(min = 1))]
    pub max_guests: u32,
    #[validate(range(min = 1))]
    pub bedrooms: u32,
    #[validate(range(min = 1))]
    pub bathrooms: u32,
    pub amenities: Vec<String>,
    pub images: Vec<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdatePropertyRequest {
    #[validate(length(min = 1, max = 200))]
    pub title: Option<String>,
    #[validate(length(min = 10, max = 2000))]
    pub description: Option<String>,
    pub property_type: Option<PropertyType>,
    pub address: Option<Address>,
    #[validate(range(min = 0.01))]
    pub price_per_night: Option<Decimal>,
    #[validate(range(min = 1))]
    pub max_guests: Option<u32>,
    #[validate(range(min = 1))]
    pub bedrooms: Option<u32>,
    #[validate(range(min = 1))]
    pub bathrooms: Option<u32>,
    pub amenities: Option<Vec<String>>,
    pub images: Option<Vec<String>>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct PropertySearchParams {
    pub city: Option<String>,
    pub country: Option<String>,
    pub property_type: Option<PropertyType>,
    pub min_price: Option<Decimal>,
    pub max_price: Option<Decimal>,
    pub min_guests: Option<u32>,
    pub amenities: Option<Vec<String>>,
    pub check_in: Option<NaiveDate>,
    pub check_out: Option<NaiveDate>,
    #[serde(flatten)]
    pub pagination: PaginationParams,
}

#[derive(Debug, Deserialize)]
pub struct AvailabilityRequest {
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
}

impl Property {
    pub fn new(host_id: Uuid, request: CreatePropertyRequest) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            host_id,
            title: request.title,
            description: request.description,
            property_type: request.property_type,
            address: request.address,
            price_per_night: request.price_per_night,
            max_guests: request.max_guests,
            bedrooms: request.bedrooms,
            bathrooms: request.bathrooms,
            amenities: request.amenities,
            images: request.images,
            availability: Vec::new(),
            average_rating: None,
            total_reviews: 0,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update(&mut self, request: UpdatePropertyRequest) {
        if let Some(title) = request.title {
            self.title = title;
        }
        if let Some(description) = request.description {
            self.description = description;
        }
        if let Some(property_type) = request.property_type {
            self.property_type = property_type;
        }
        if let Some(address) = request.address {
            self.address = address;
        }
        if let Some(price_per_night) = request.price_per_night {
            self.price_per_night = price_per_night;
        }
        if let Some(max_guests) = request.max_guests {
            self.max_guests = max_guests;
        }
        if let Some(bedrooms) = request.bedrooms {
            self.bedrooms = bedrooms;
        }
        if let Some(bathrooms) = request.bathrooms {
            self.bathrooms = bathrooms;
        }
        if let Some(amenities) = request.amenities {
            self.amenities = amenities;
        }
        if let Some(images) = request.images {
            self.images = images;
        }
        self.updated_at = Utc::now();
    }

    pub fn is_available(&self, start_date: NaiveDate, end_date: NaiveDate) -> bool {
        let mut current_date = start_date;
        while current_date <= end_date {
            if let Some(availability) = self.availability.iter().find(|a| a.date == current_date) {
                if !availability.available {
                    return false;
                }
            }
            current_date = current_date.succ_opt().unwrap_or(current_date);
        }
        true
    }

    pub fn update_rating(&mut self, new_rating: f64) {
        if let Some(current_avg) = self.average_rating {
            self.average_rating = Some(
                (current_avg * self.total_reviews as f64 + new_rating) / (self.total_reviews + 1) as f64
            );
        } else {
            self.average_rating = Some(new_rating);
        }
        self.total_reviews += 1;
    }
}
