use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;

use super::common::{BookingType, PaginationParams};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Review {
    pub id: Uuid,
    pub user_id: Uuid,
    pub resource_id: Uuid, // property_id or car_id
    pub resource_type: BookingType,
    pub rating: u8, // 1-5 stars
    pub comment: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct ReviewResponse {
    pub id: Uuid,
    pub user_id: Uuid,
    pub user_name: String,
    pub user_avatar: Option<String>,
    pub resource_id: Uuid,
    pub resource_type: BookingType,
    pub rating: u8,
    pub comment: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateReviewRequest {
    #[validate(range(min = 1, max = 5))]
    pub rating: u8,
    #[validate(length(min = 10, max = 1000))]
    pub comment: String,
}

#[derive(Debug, Deserialize)]
pub struct ReviewSearchParams {
    pub rating: Option<u8>,
    pub user_id: Option<Uuid>,
    #[serde(flatten)]
    pub pagination: PaginationParams,
}

#[derive(Debug, Serialize)]
pub struct ReviewSummary {
    pub total_reviews: u32,
    pub average_rating: f64,
    pub rating_distribution: RatingDistribution,
}

#[derive(Debug, Serialize)]
pub struct RatingDistribution {
    pub five_star: u32,
    pub four_star: u32,
    pub three_star: u32,
    pub two_star: u32,
    pub one_star: u32,
}

impl Review {
    pub fn new_property_review(
        user_id: Uuid,
        property_id: Uuid,
        request: CreateReviewRequest,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            user_id,
            resource_id: property_id,
            resource_type: BookingType::Property,
            rating: request.rating,
            comment: request.comment,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn new_car_review(
        user_id: Uuid,
        car_id: Uuid,
        request: CreateReviewRequest,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            user_id,
            resource_id: car_id,
            resource_type: BookingType::Car,
            rating: request.rating,
            comment: request.comment,
            created_at: now,
            updated_at: now,
        }
    }
}

impl RatingDistribution {
    pub fn new() -> Self {
        Self {
            five_star: 0,
            four_star: 0,
            three_star: 0,
            two_star: 0,
            one_star: 0,
        }
    }

    pub fn add_rating(&mut self, rating: u8) {
        match rating {
            5 => self.five_star += 1,
            4 => self.four_star += 1,
            3 => self.three_star += 1,
            2 => self.two_star += 1,
            1 => self.one_star += 1,
            _ => {}
        }
    }

    pub fn total(&self) -> u32 {
        self.five_star + self.four_star + self.three_star + self.two_star + self.one_star
    }
}

impl ReviewSummary {
    pub fn new(reviews: &[Review]) -> Self {
        let mut distribution = RatingDistribution::new();
        let mut total_rating = 0u32;

        for review in reviews {
            distribution.add_rating(review.rating);
            total_rating += review.rating as u32;
        }

        let average_rating = if reviews.is_empty() {
            0.0
        } else {
            total_rating as f64 / reviews.len() as f64
        };

        Self {
            total_reviews: reviews.len() as u32,
            average_rating,
            rating_distribution: distribution,
        }
    }
}
