use axum::{
    extract::{Path, Query, State, Extension},
    response::<PERSON><PERSON>,
};
use uuid::Uuid;

use crate::{
    errors::Result,
    models::{
        CreateCarRequest, UpdateCarRequest, CarSearchParams,
        CarAvailabilityRequest, ApiResponse, Car, PaginatedResponse, Availability
    },
    middleware::auth::AuthUser,
    AppState,
};

pub async fn create_car(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreateCarRequest>,
) -> Result<Json<ApiResponse<Car>>> {
    let car = state
        .car_service
        .create_car(auth_user.id, request)
        .await?;
    Ok(Json(ApiResponse::success(car)))
}

pub async fn get_car(
    State(state): State<AppState>,
    Path(car_id): Path<Uuid>,
) -> Result<Json<ApiResponse<Car>>> {
    let car = state.car_service.get_car(car_id).await?;
    Ok(Json(ApiResponse::success(car)))
}

pub async fn update_car(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(car_id): Path<Uuid>,
    Json(request): Json<UpdateCarRequest>,
) -> Result<Json<ApiResponse<Car>>> {
    let car = state
        .car_service
        .update_car(car_id, auth_user.id, request)
        .await?;
    Ok(Json(ApiResponse::success(car)))
}

pub async fn delete_car(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(car_id): Path<Uuid>,
) -> Result<Json<ApiResponse<String>>> {
    let deleted = state
        .car_service
        .delete_car(car_id, auth_user.id)
        .await?;
    
    if deleted {
        Ok(Json(ApiResponse::success("Car deleted successfully".to_string())))
    } else {
        Ok(Json(ApiResponse::error("Failed to delete car".to_string())))
    }
}

pub async fn list_cars(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<Car>>>> {
    let cars = state.car_service.list_cars().await?;
    Ok(Json(ApiResponse::success(cars)))
}

pub async fn search_cars(
    State(state): State<AppState>,
    Query(params): Query<CarSearchParams>,
) -> Result<Json<ApiResponse<PaginatedResponse<Car>>>> {
    let cars = state.car_service.search_cars(params).await?;
    Ok(Json(ApiResponse::success(cars)))
}

pub async fn get_availability(
    State(state): State<AppState>,
    Path(car_id): Path<Uuid>,
    Query(request): Query<CarAvailabilityRequest>,
) -> Result<Json<ApiResponse<Vec<Availability>>>> {
    let availability = state
        .car_service
        .get_car_availability(car_id, request)
        .await?;
    Ok(Json(ApiResponse::success(availability)))
}
