use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Dec<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::{
    config::Config,
    errors::{AppError, Result},
    models::{User, CreateUserRequest, LoginRequest, LoginResponse, UserResponse},
    repositories::UserRepository,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String, // user id
    pub email: String,
    pub role: String,
    pub exp: i64,
    pub iat: i64,
}

#[derive(Clone)]
pub struct AuthService {
    user_repository: UserRepository,
    config: Config,
}

impl AuthService {
    pub fn new(user_repository: UserRepository, config: Config) -> Self {
        Self {
            user_repository,
            config,
        }
    }

    pub async fn register(&self, request: CreateUserRequest) -> Result<LoginResponse> {
        // Hash the password
        let password_hash = bcrypt::hash(&request.password, self.config.bcrypt_cost)?;

        // Create user
        let user = User::new(request, password_hash);
        let created_user = self.user_repository.create(user).await?;

        // Generate JWT token
        let token = self.generate_token(&created_user)?;
        let expires_at = Utc::now() + Duration::seconds(self.config.jwt_expiration);

        Ok(LoginResponse {
            user: created_user.into(),
            token,
            expires_at,
        })
    }

    pub async fn login(&self, request: LoginRequest) -> Result<LoginResponse> {
        // Find user by email
        let user = self
            .user_repository
            .find_by_email(&request.email)
            .await?
            .ok_or(AppError::Unauthorized)?;

        // Verify password
        if !bcrypt::verify(&request.password, &user.password_hash)? {
            return Err(AppError::Unauthorized);
        }

        // Generate JWT token
        let token = self.generate_token(&user)?;
        let expires_at = Utc::now() + Duration::seconds(self.config.jwt_expiration);

        Ok(LoginResponse {
            user: user.into(),
            token,
            expires_at,
        })
    }

    pub async fn verify_token(&self, token: &str) -> Result<Claims> {
        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.config.jwt_secret.as_ref()),
            &Validation::default(),
        )?;

        Ok(token_data.claims)
    }

    pub async fn get_user_from_token(&self, token: &str) -> Result<User> {
        let claims = self.verify_token(token).await?;
        let user_id = Uuid::parse_str(&claims.sub)
            .map_err(|_| AppError::Unauthorized)?;

        self.user_repository
            .find_by_id(user_id)
            .await?
            .ok_or(AppError::Unauthorized)
    }

    pub async fn refresh_token(&self, token: &str) -> Result<LoginResponse> {
        let user = self.get_user_from_token(token).await?;
        let new_token = self.generate_token(&user)?;
        let expires_at = Utc::now() + Duration::seconds(self.config.jwt_expiration);

        Ok(LoginResponse {
            user: user.into(),
            token: new_token,
            expires_at,
        })
    }

    fn generate_token(&self, user: &User) -> Result<String> {
        let now = Utc::now();
        let claims = Claims {
            sub: user.id.to_string(),
            email: user.email.clone(),
            role: format!("{:?}", user.role),
            exp: (now + Duration::seconds(self.config.jwt_expiration)).timestamp(),
            iat: now.timestamp(),
        };

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.config.jwt_secret.as_ref()),
        )?;

        Ok(token)
    }
}
