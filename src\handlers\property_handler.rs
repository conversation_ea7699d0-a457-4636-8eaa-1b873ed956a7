use axum::{
    extract::{Path, Query, State, Extension},
    response::<PERSON><PERSON>,
};
use uuid::Uuid;

use crate::{
    errors::Result,
    models::{
        CreatePropertyRequest, UpdatePropertyRequest, PropertySearchParams,
        AvailabilityRequest, ApiResponse, Property, PaginatedResponse, Availability
    },
    middleware::auth::AuthUser,
    AppState,
};

pub async fn create_property(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<CreatePropertyRequest>,
) -> Result<Json<ApiResponse<Property>>> {
    let property = state
        .property_service
        .create_property(auth_user.id, request)
        .await?;
    Ok(Json(ApiResponse::success(property)))
}

pub async fn get_property(
    State(state): State<AppState>,
    Path(property_id): Path<Uuid>,
) -> Result<Json<ApiResponse<Property>>> {
    let property = state.property_service.get_property(property_id).await?;
    Ok(Json(ApiResponse::success(property)))
}

pub async fn update_property(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(property_id): Path<Uuid>,
    Json(request): Json<UpdatePropertyRequest>,
) -> Result<Json<ApiResponse<Property>>> {
    let property = state
        .property_service
        .update_property(property_id, auth_user.id, request)
        .await?;
    Ok(Json(ApiResponse::success(property)))
}

pub async fn delete_property(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(property_id): Path<Uuid>,
) -> Result<Json<ApiResponse<String>>> {
    let deleted = state
        .property_service
        .delete_property(property_id, auth_user.id)
        .await?;
    
    if deleted {
        Ok(Json(ApiResponse::success("Property deleted successfully".to_string())))
    } else {
        Ok(Json(ApiResponse::error("Failed to delete property".to_string())))
    }
}

pub async fn list_properties(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<Property>>>> {
    let properties = state.property_service.list_properties().await?;
    Ok(Json(ApiResponse::success(properties)))
}

pub async fn search_properties(
    State(state): State<AppState>,
    Query(params): Query<PropertySearchParams>,
) -> Result<Json<ApiResponse<PaginatedResponse<Property>>>> {
    let properties = state.property_service.search_properties(params).await?;
    Ok(Json(ApiResponse::success(properties)))
}

pub async fn get_availability(
    State(state): State<AppState>,
    Path(property_id): Path<Uuid>,
    Query(request): Query<AvailabilityRequest>,
) -> Result<Json<ApiResponse<Vec<Availability>>>> {
    let availability = state
        .property_service
        .get_property_availability(property_id, request)
        .await?;
    Ok(Json(ApiResponse::success(availability)))
}
