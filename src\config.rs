use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub port: u16,
    pub jwt_secret: String,
    pub jwt_expiration: i64, // in seconds
    pub bcrypt_cost: u32,
    pub data_dir: String,
}

impl Config {
    pub fn new() -> anyhow::Result<Self> {
        Ok(Config {
            port: env::var("PORT")
                .unwrap_or_else(|_| "3000".to_string())
                .parse()
                .unwrap_or(3000),
            jwt_secret: env::var("JWT_SECRET")
                .unwrap_or_else(|_| "your-super-secret-jwt-key-change-in-production".to_string()),
            jwt_expiration: env::var("JWT_EXPIRATION")
                .unwrap_or_else(|_| "86400".to_string()) // 24 hours
                .parse()
                .unwrap_or(86400),
            bcrypt_cost: env::var("BCRYPT_COST")
                .unwrap_or_else(|_| "12".to_string())
                .parse()
                .unwrap_or(12),
            data_dir: env::var("DATA_DIR")
                .unwrap_or_else(|_| "data".to_string()),
        })
    }
}
