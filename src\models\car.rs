use chrono::{DateTime, Utc, NaiveDate};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;

use super::common::{Location, Availability, CarCategory, Transmission, FuelType, PaginationParams};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Car {
    pub id: Uuid,
    pub owner_id: Uuid,
    pub make: String,
    pub model: String,
    pub year: u32,
    pub category: CarCategory,
    pub transmission: Transmission,
    pub fuel_type: FuelType,
    pub seats: u32,
    pub price_per_day: Decimal,
    pub features: Vec<String>,
    pub images: Vec<String>,
    pub location: Location,
    pub availability: Vec<Availability>,
    pub average_rating: Option<f64>,
    pub total_reviews: u32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateCarRequest {
    #[validate(length(min = 1, max = 50))]
    pub make: String,
    #[validate(length(min = 1, max = 50))]
    pub model: String,
    #[validate(range(min = 1900, max = 2030))]
    pub year: u32,
    pub category: CarCategory,
    pub transmission: Transmission,
    pub fuel_type: FuelType,
    #[validate(range(min = 2, max = 8))]
    pub seats: u32,
    #[validate(range(min = 0.01))]
    pub price_per_day: Decimal,
    pub features: Vec<String>,
    pub images: Vec<String>,
    pub location: Location,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateCarRequest {
    #[validate(length(min = 1, max = 50))]
    pub make: Option<String>,
    #[validate(length(min = 1, max = 50))]
    pub model: Option<String>,
    #[validate(range(min = 1900, max = 2030))]
    pub year: Option<u32>,
    pub category: Option<CarCategory>,
    pub transmission: Option<Transmission>,
    pub fuel_type: Option<FuelType>,
    #[validate(range(min = 2, max = 8))]
    pub seats: Option<u32>,
    #[validate(range(min = 0.01))]
    pub price_per_day: Option<Decimal>,
    pub features: Option<Vec<String>>,
    pub images: Option<Vec<String>>,
    pub location: Option<Location>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CarSearchParams {
    pub city: Option<String>,
    pub country: Option<String>,
    pub category: Option<CarCategory>,
    pub transmission: Option<Transmission>,
    pub fuel_type: Option<FuelType>,
    pub min_price: Option<Decimal>,
    pub max_price: Option<Decimal>,
    pub min_seats: Option<u32>,
    pub features: Option<Vec<String>>,
    pub pickup_date: Option<NaiveDate>,
    pub return_date: Option<NaiveDate>,
    #[serde(flatten)]
    pub pagination: PaginationParams,
}

#[derive(Debug, Deserialize)]
pub struct CarAvailabilityRequest {
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
}

impl Car {
    pub fn new(owner_id: Uuid, request: CreateCarRequest) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            owner_id,
            make: request.make,
            model: request.model,
            year: request.year,
            category: request.category,
            transmission: request.transmission,
            fuel_type: request.fuel_type,
            seats: request.seats,
            price_per_day: request.price_per_day,
            features: request.features,
            images: request.images,
            location: request.location,
            availability: Vec::new(),
            average_rating: None,
            total_reviews: 0,
            created_at: now,
            updated_at: now,
        }
    }

    pub fn update(&mut self, request: UpdateCarRequest) {
        if let Some(make) = request.make {
            self.make = make;
        }
        if let Some(model) = request.model {
            self.model = model;
        }
        if let Some(year) = request.year {
            self.year = year;
        }
        if let Some(category) = request.category {
            self.category = category;
        }
        if let Some(transmission) = request.transmission {
            self.transmission = transmission;
        }
        if let Some(fuel_type) = request.fuel_type {
            self.fuel_type = fuel_type;
        }
        if let Some(seats) = request.seats {
            self.seats = seats;
        }
        if let Some(price_per_day) = request.price_per_day {
            self.price_per_day = price_per_day;
        }
        if let Some(features) = request.features {
            self.features = features;
        }
        if let Some(images) = request.images {
            self.images = images;
        }
        if let Some(location) = request.location {
            self.location = location;
        }
        self.updated_at = Utc::now();
    }

    pub fn is_available(&self, start_date: NaiveDate, end_date: NaiveDate) -> bool {
        let mut current_date = start_date;
        while current_date <= end_date {
            if let Some(availability) = self.availability.iter().find(|a| a.date == current_date) {
                if !availability.available {
                    return false;
                }
            }
            current_date = current_date.succ_opt().unwrap_or(current_date);
        }
        true
    }

    pub fn update_rating(&mut self, new_rating: f64) {
        if let Some(current_avg) = self.average_rating {
            self.average_rating = Some(
                (current_avg * self.total_reviews as f64 + new_rating) / (self.total_reviews + 1) as f64
            );
        } else {
            self.average_rating = Some(new_rating);
        }
        self.total_reviews += 1;
    }

    pub fn display_name(&self) -> String {
        format!("{} {} {}", self.year, self.make, self.model)
    }
}
