use axum::{
    extract::{Path, State, Extension},
    response::<PERSON><PERSON>,
};
use uuid::Uuid;

use crate::{
    errors::Result,
    models::{UpdateUserRequest, ApiResponse, UserResponse},
    middleware::auth::AuthUser,
    AppState,
};

pub async fn get_profile(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
) -> Result<Json<ApiResponse<UserResponse>>> {
    let user = state.user_service.get_user_profile(auth_user.id).await?;
    Ok(Json(ApiResponse::success(user)))
}

pub async fn update_profile(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Json(request): Json<UpdateUserRequest>,
) -> Result<Json<ApiResponse<UserResponse>>> {
    let user = state
        .user_service
        .update_user_profile(auth_user.id, request)
        .await?;
    Ok(<PERSON><PERSON>(ApiResponse::success(user)))
}

pub async fn get_user(
    State(state): State<AppState>,
    Path(user_id): Path<Uuid>,
) -> Result<Json<ApiResponse<UserResponse>>> {
    let user = state.user_service.get_user_by_id(user_id).await?;
    Ok(Json(ApiResponse::success(user)))
}
